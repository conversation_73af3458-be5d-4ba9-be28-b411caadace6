# Tech Context: sendmeDungeonMaster

## Linguagem e Ambiente

-   **Linguagem:** Python 3.12+
-   **Gerenciador de Dependências:** `uv`.

## Dependências Principais

-   **`discord.py`:** Para interação com a API do Discord.
-   **`google-generativeai`:** SDK para a API do Gemini.
-   **`python-dotenv`:** Para gerenciamento de variáveis de ambiente.

## Estrutura de Armazenamento

-   **Formato:** CSV para dados de campanhas e protagonistas; Markdown para checkpoints.

## Configuração e Execução

1.  **Instalação:** `uv venv` e `uv sync`.
2.  **Configuração:** Criar arquivo `.env` com `DISCORD_TOKEN` e `GEMINI_API_KEY`.
3.  **Execução:** `source .venv/bin/activate` e `python src/main.py`.

## Interação com o Bot

### Comandos

-   `/ping`: Verifica a latência.
-   `/criarprotagonista`: Abre um formulário em duas etapas para a criação completa de um personagem.
-   `/novacampanha protagonistas:[IDs]`: Inicia uma campanha.
-   `/checkpoint id_campanha:[ID] descricao:[Texto]`: Salva um checkpoint.

### Formato da Ação do Jogador

Ao usar a opção "Outra Ação...", você pode formatar sua entrada para guiar a IA:

-   **Falas do personagem:** Use aspas. Ex: `"Qual é o seu preço?"`
-   **Ações do personagem:** Descreva livremente o que quer fazer. Ex: `Tento arrombar a porta silenciosamente.`
-   **Comentários fora do personagem (OOC):** Use colchetes angulares. Ex: `<Poderia me lembrar qual era o nome do nobre?>`