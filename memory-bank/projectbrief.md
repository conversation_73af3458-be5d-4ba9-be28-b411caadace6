# Project Brief: sendmeDungeonMaster

## Visão Geral

`sendmeDungeonMaster` é um bot para Discord projetado para criar, narrar e gerenciar campanhas de RPG de mesa. Ele utiliza a API do Google Gemini para gerar conteúdo narrativo dinâmico com base em parâmetros definidos pelo usuário, como tema, tom e cenário.

## Objetivos Principais

1. **Geração de Campanhas:** Permitir que usuários criem campanhas de RPG personalizadas através de comandos interativos no Discord.
2. **Narração Dinâmica:** Usar a IA do Gemini para gerar a introdução e os eventos da campanha, atuando como um "mestre de jogo" automatizado.
3. **Gerenciamento de Dados:** Salvar de forma persistente os dados das campanhas e dos personagens (protagonistas) em arquivos CSV.
4. **Registro de Progresso:** Oferecer uma funcionalidade de "checkpoint" para que os usuários possam salvar acontecimentos importantes da história em arquivos Markdown.
5. **Estrutura Modular:** Construir o bot com uma arquitetura de código limpa e modular, separando a lógica do bot, o gerenciamento de dados e a integração com a API externa.
