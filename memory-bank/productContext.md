# Product Context: sendmeDungeonMaster

## Problema a Ser Resolvido

Muitos grupos de RPG lutam para manter a consistência e o ritmo de suas campanhas, seja pela dificuldade de agendamento, pela carga de preparação para o mestre de jogo (Dungeon Master), ou pela falta de ferramentas para registrar o progresso da história de forma simples. Este projeto visa mitigar esses problemas, oferecendo um assistente automatizado.

## Como o Bot Soluciona o Problema

O `sendmeDungeonMaster` atua como um mestre de jogo assistente, com as seguintes funções:

1. **Reduz a Carga de Preparação:** Ao gerar introduções de campanha e ganchos de história dinamicamente com a IA do Gemini, o bot diminui o tempo que o mestre humano precisa gastar na preparação.
2. **Facilita a Criação de Personagens:** O fluxo interativo para criar protagonistas guia os jogadores, garantindo que todos os detalhes importantes sejam registrados.
3. **Centraliza as Informações:** Os dados de campanhas e personagens são armazenados de forma organizada, evitando a perda de informações.
4. **Cria um Diário de Aventura:** O sistema de checkpoints em Markdown funciona como um diário da campanha, permitindo que os jogadores relembrem eventos importantes facilmente.

## Experiência do Usuário

A interação com o bot deve ser simples e conversacional. Os usuários interagem através de comandos claros no Discord (ex: `/novacampanha`, `/criarprotagonista`, `/checkpoint`). O bot responde de forma interativa, solicitando as informações necessárias passo a passo e fornecendo feedback claro sobre o sucesso ou falha das operações.
