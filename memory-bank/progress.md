# Progress: sendmeDungeonMaster

## O que Funciona

A versão atual do bot (`v4.0`) implementa um fluxo de criação de personagem, loop de jogo multijogador e gerenciamento de sessão.

- **Criação de Personagem em Duas Etapas:** O comando `/criarprotagonista` usa dois formulários para coletar todos os detalhes do personagem.
- **Experiência Multijogador:**
  - O comando `/novacampanha` cria um lobby para até 4 jogadores entrarem.
  - O bot aguarda a ação de todos os jogadores antes de avançar o turno.
  - Possui um timeout de 10 minutos para forçar o avanço do turno.
- **Persistência de Sessão e Checkpoints:**
  - O estado completo de todas as campanhas (jogadores, narrativa, status) é salvo em arquivos JSON na pasta `data/sessions`.
  - As sessões são recarregadas automaticamente se o bot reiniciar.
  - O comando `/checkpoint` agora salva o estado completo da sessão atual.
  - O novo comando `/carregarcampanha` permite carregar uma sessão a partir de um checkpoint salvo.
  - Os comandos `/pausarcampanha` e `/continuarcampanha` permitem gerenciar o estado da sessão.
- **Mestre de Jogo Interativo:** O bot conduz a campanha, apresentando uma narrativa e ações sugeridas.
- **Ação do Jogador Flexível:** O jogador pode escolher uma ação sugerida ou descrever uma ação personalizada.
- **Narrativa Contínua e Contextual:** O bot mantém o contexto da última cena e do histórico dos personagens para gerar a continuação da história.
- **Interface com Slash Commands:** Toda a interação é feita através de uma interface moderna.

## O que Falta Construir

- [ ] **Comandos de Gerenciamento:** `/editarprotagonista`, `/verprotagonista`, etc.

## Decisões do Projeto

- **Estado em JSON:** A decisão de usar arquivos JSON para salvar o estado de cada sessão (`CampaignState`) oferece flexibilidade e facilita a depuração, desacoplando o estado da lógica do bot.
- **Formulários em Sequência:** A decisão de usar dois Modais em sequência para a criação de personagem foi a solução mais elegante para contornar a limitação da API do Discord.
- **Inteligência no Prompt:** A lógica do RPG continua delegada à IA através de *prompt engineering*.
