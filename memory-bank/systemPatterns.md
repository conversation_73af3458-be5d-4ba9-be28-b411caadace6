# System Patterns: sendmeDungeonMaster

## Arquitetura Geral

O sistema é projetado em torno de uma arquitetura modular em Python, com os seguintes componentes:

- **<PERSON> do Bot (`bot.py`):** Utiliza `discord.Client` e `app_commands.CommandTree` para registrar *slash commands*. Contém a lógica de interação, incluindo `Views` e `Modals` para gerenciar o fluxo do jogo.
- **<PERSON>liente da IA (`gemini_client.py`):** Encapsula a comunicação com a API do Gemini, contendo funções para construir os prompts de início e de continuação da campanha.
- **Gerenciador de Dados (`data_manager.py`):** Abstrai as operações de I/O para os arquivos de dados (CSVs).

## Padrões de Design

- **Loop de Jogo Assíncrono:** O núcleo do bot é um loop de jogo onde o bot apresenta uma narrativa e um conjunto de ações (via `discord.ui.View` com botões), aguarda a entrada do jogador e, em seguida, gera o próximo estado do jogo.
- **Gerenciamento de Estado via Callbacks:** O estado da sessão de jogo (como a narrativa mais recente) é passado através dos callbacks dos componentes da UI (botões e modais), evitando a necessidade de um armazenamento de estado global complexo.
- **Separação de Responsabilidades (SoC):** `bot.py` cuida da interface e do fluxo, `gemini_client.py` da lógica da IA, e `data_manager.py` da persistência de longo prazo.
- **Coleta de Dados com Modais:** Formulários (`discord.ui.Modal`) são usados para coletar dados complexos do usuário de forma estruturada.

## Fluxo de Dados: Loop de Continuação

```mermaid
sequenceDiagram
    participant User as Usuário
    participant View as ActionView (Botões)
    participant Bot as bot.py
    participant Gemini as gemini_client.py

    User->>View: Clica no botão de Ação
    View->>Bot: advance_story(interaction, campaign_id, last_narrative, action)
    
    Bot->>Bot: Desativa a View antiga
    Bot->>Gemini: build_continuation_prompt(last_narrative, action)
    Gemini-->>Bot: Retorna prompt de continuação
    
    Bot->>Gemini: generate_campaign_details(prompt)
    Gemini-->>Bot: Retorna nova narrativa e novas ações
    
    Bot->>Bot: parse_ia_response()
    
    Bot->>User: Envia nova narrativa e uma nova ActionView com novos botões
