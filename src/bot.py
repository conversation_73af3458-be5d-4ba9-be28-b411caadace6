import os
import re
import discord
import time
import asyncio
import logging
from discord import app_commands
from dotenv import load_dotenv

# Configuração de logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# Limite de caracteres do Discord
DISCORD_MESSAGE_LIMIT = 2000

# --- Modelos de Dados e Estado ---
from src.state_models import CampaignState

# --- Gerenciamento de Estado ---
active_campaigns: dict[str, CampaignState] = {}

import src.data_manager as data_manager
import src.gemini_client as gemini_client

# Carrega as variáveis de ambiente
load_dotenv()
DISCORD_TOKEN = os.getenv("DISCORD_TOKEN")

# --- Funções Auxiliares ---
def parse_ia_response(response: str) -> tuple[str, list[str]]:
    """Extrai a narrativa e as ações da resposta da IA."""
    actions = re.findall(r'\{\{([^}]+)\}\}', response)
    narrative = re.sub(r'\d+\.\s*\{\{.*\}\}\n?', '', response).strip()
    return narrative, actions

async def send_long_message(interaction_followup: discord.Webhook, text: str, view: discord.ui.View | None = None):
    """Envia uma mensagem longa, dividindo-a se necessário."""
    # (código da função inalterado)
    if len(text) <= DISCORD_MESSAGE_LIMIT:
        await interaction_followup.send(text, view=view if view else discord.utils.MISSING, ephemeral=False)
        return
    parts = []
    current_pos = 0
    while current_pos < len(text):
        end_pos = current_pos + DISCORD_MESSAGE_LIMIT
        if end_pos >= len(text):
            parts.append(text[current_pos:])
            break
        last_newline = text.rfind('\\n', current_pos, end_pos)
        if last_newline != -1:
            parts.append(text[current_pos:last_newline])
            current_pos = last_newline + 1
        else:
            parts.append(text[current_pos:end_pos])
            current_pos = end_pos
    for i, part in enumerate(parts):
        if i == len(parts) - 1 and view:
            await interaction_followup.send(part, view=view, ephemeral=False)
        else:
            await interaction_followup.send(part, ephemeral=False)

# --- Loop de Jogo ---
async def advance_story(campaign_id: str, followup: discord.Webhook | discord.TextChannel):
    """
    Avança a história da campanha, enviando a resposta para o webhook ou canal fornecido.
    """
    state = active_campaigns.get(campaign_id)
    if not state or state.status != "running":
        return

    try:
        protagonistas_data = {}
        for user_id, protagonista_id in state.players.items():
            protagonista = data_manager.get_protagonista(protagonista_id)
            if protagonista:
                protagonistas_data[user_id] = protagonista

        # Filtra ações nulas para garantir a tipagem correta
        actions_to_send = {user_id: action for user_id, action in state.player_actions.items() if action is not None}

        prompt = gemini_client.build_continuation_prompt(
            previous_narrative=state.narrative,
            player_actions=actions_to_send,
            protagonistas=protagonistas_data
        )
        response_text = gemini_client.generate_campaign_details(prompt)
        new_narrative, new_actions = parse_ia_response(response_text)

        state.narrative = new_narrative
        state.actions = new_actions
        state.player_actions = {player_id: None for player_id in state.players}
        state.turn_start_time = time.time()

        if state.turn_start_time:
            asyncio.create_task(turn_timeout_watcher(campaign_id, state.turn_start_time))

        view = ActionView(campaign_id=campaign_id, narrative=new_narrative, actions=new_actions)
        
        # Envia a mensagem para o followup (se for de uma interação) ou para o canal diretamente
        if isinstance(followup, discord.Webhook):
            # Esta função send_long_message precisa ser adaptada para Webhook
            await send_long_message(followup, new_narrative, view=view)
        elif isinstance(followup, discord.TextChannel):
            # Esta função send_long_message não existe para TextChannel, vamos usar a nativa.
            # Idealmente, send_long_message seria refatorada para aceitar ambos.
            if len(new_narrative) <= DISCORD_MESSAGE_LIMIT:
                message = await followup.send(new_narrative, view=view)
                state.message_id = message.id # Atualiza o ID da mensagem de estado
            else: # Lógica simples de divisão para o canal
                parts = [new_narrative[i:i+DISCORD_MESSAGE_LIMIT] for i in range(0, len(new_narrative), DISCORD_MESSAGE_LIMIT)]
                for i, part in enumerate(parts):
                    if i == len(parts) - 1: # Adiciona a view na última parte
                        message = await followup.send(part, view=view)
                        state.message_id = message.id
                    else:
                        await followup.send(part)

        # Incrementa o contador de turnos e verifica se é hora de gerar um resumo
        state.turn_count += 1
        if state.turn_count % state.summary_interval == 0:
            logging.info(f"Gerando resumo para a campanha '{campaign_id}' no turno {state.turn_count}.")
            # A narrativa completa para o resumo pode ser construída lendo o arquivo de resumo existente
            # e adicionando a narrativa atual.
            summary_file_path = os.path.join(data_manager.SUMMARIES_DIR, f"{campaign_id}.txt")
            full_narrative_for_summary = ""
            if os.path.exists(summary_file_path):
                with open(summary_file_path, 'r', encoding='utf-8') as f:
                    full_narrative_for_summary = f.read()
            full_narrative_for_summary += "\\n\\n" + new_narrative
            
            summary_prompt = gemini_client.build_summary_prompt(full_narrative_for_summary)
            summary_text = gemini_client.generate_summary(summary_prompt)
            data_manager.save_summary(campaign_id, summary_text)

    except Exception as e:
        error_message = f"Ocorreu um erro ao avançar a história: {e}"
        if isinstance(followup, discord.Webhook):
            await followup.send(error_message, ephemeral=True)
        elif isinstance(followup, discord.TextChannel):
            await followup.send(error_message)
    finally:
        # Salva o estado após cada turno
        if state:
            data_manager.save_campaign_state(state)


async def check_and_advance_turn(interaction: discord.Interaction, campaign_id: str):
    """
    Verifica se todos os jogadores agiram. Se sim, avança a história.
    """
    state = active_campaigns.get(campaign_id)
    if not state:
        logging.warning(f"check_and_advance_turn: Estado da campanha '{campaign_id}' não encontrado.")
        return

    logging.info(f"check_and_advance_turn para campanha '{campaign_id}'. Estado das ações: {state.player_actions}")

    # Verifica se todos os jogadores no estado 'running' já enviaram uma ação
    if state.player_actions and all(action is not None for action in state.player_actions.values()):
        logging.info(f"Todos os jogadores agiram na campanha '{campaign_id}'. Avançando o turno.")
        # Defer a resposta para que o bot tenha tempo de pensar
        await interaction.response.defer()
        # Edita a mensagem para mostrar que o bot está processando
        if interaction.message:
            await interaction.message.edit(content=f"Todos os jogadores agiram! Processando o próximo turno...", view=None)
        await advance_story(campaign_id, interaction.followup)
    else:
        # Atualiza a mensagem para mostrar quem ainda falta jogar
        await interaction.response.defer() # Acknowledge o clique do botão
        if interaction.message:
            jogadores_faltantes = [f"<@{uid}>" for uid, action in state.player_actions.items() if action is None]
            await interaction.message.edit(
                content=f"{state.narrative}\n\n**Aguardando ação de:** {', '.join(jogadores_faltantes)}"
            )


async def turn_timeout_watcher(campaign_id: str, turn_timestamp: float):
    """Observa o turno e o força a avançar após 10 minutos."""
    await asyncio.sleep(600) # 10 minutos

    state = active_campaigns.get(campaign_id)
    logging.info(f"Timeout watcher para campanha '{campaign_id}' iniciado com timestamp {turn_timestamp}.")
    # Verifica se a campanha ainda existe e se o turno não foi atualizado nesse meio tempo
    if not state or state.turn_start_time != turn_timestamp:
        logging.info(f"Timeout watcher para campanha '{campaign_id}' obsoleto. Saindo.")
        return
    
    logging.info(f"Timeout de 10 minutos atingido para campanha '{campaign_id}'. Verificando ações.")

    # Pega o canal para enviar a mensagem
    if not state.channel_id: return
    channel = client.get_channel(state.channel_id)
    if not channel or not isinstance(channel, discord.TextChannel):
        return

    # Define uma ação padrão para jogadores ausentes e verifica se algum jogador agiu
    jogadores_ausentes = []
    algum_jogador_agiu = False
    for user_id, action in state.player_actions.items():
        if action is None:
            state.player_actions[user_id] = "(Permanece em silêncio, observando)"
            jogadores_ausentes.append(f"<@{user_id}>")
        else:
            algum_jogador_agiu = True

    # Só avança o turno se houver jogadores ausentes.
    # Se todos agiram, o turno já avançou. Se ninguém agiu, não faz nada.
    if jogadores_ausentes and algum_jogador_agiu:
        await channel.send(f"O tempo para agir esgotou! Avançando o turno. {', '.join(jogadores_ausentes)} não agiram.")
        await advance_story(campaign_id, channel)

# --- Views e Modals ---
class ActionView(discord.ui.View):
    def __init__(self, campaign_id: str, narrative: str, actions: list[str]):
        super().__init__(timeout=3600) # Timeout de 1 hora para a view
        self.campaign_id = campaign_id
        self.narrative = narrative # Mantém a narrativa para o modal de ação customizada

        # Botões de Ação Sugerida
        for i, action_text in enumerate(actions):
            button = discord.ui.Button(label=f"{i+1}", style=discord.ButtonStyle.secondary, custom_id=f"action_{campaign_id}_{i}")
            
            async def button_callback(interaction: discord.Interaction, action: str = action_text):
                state = active_campaigns.get(self.campaign_id)
                if not state or interaction.user.id not in state.players:
                    await interaction.response.send_message("Você não está nesta campanha.", ephemeral=True)
                    return
                
                state.player_actions[interaction.user.id] = action
                await check_and_advance_turn(interaction, self.campaign_id)

            button.callback = button_callback
            self.add_item(button)

        # Botão de Ação Personalizada
        other_action_button = discord.ui.Button(label="Outra Ação...", style=discord.ButtonStyle.primary, custom_id=f"other_action_{campaign_id}")
        async def other_action_callback(interaction: discord.Interaction):
            state = active_campaigns.get(self.campaign_id)
            if not state or interaction.user.id not in state.players:
                await interaction.response.send_message("Você não está nesta campanha.", ephemeral=True)
                return
            await interaction.response.send_modal(CustomActionModal(campaign_id=self.campaign_id, narrative=self.narrative))
        other_action_button.callback = other_action_callback
        self.add_item(other_action_button)

class CustomActionModal(discord.ui.Modal, title="Ação Personalizada"):
    action_input = discord.ui.TextInput(label="Descreva sua ação", style=discord.TextStyle.paragraph, required=True)

    def __init__(self, campaign_id: str, narrative: str):
        super().__init__()
        self.campaign_id = campaign_id
        self.narrative = narrative # A narrativa não é mais usada para avançar, mas pode ser útil manter

    async def on_submit(self, interaction: discord.Interaction):
        state = active_campaigns.get(self.campaign_id)
        if not state or interaction.user.id not in state.players:
            await interaction.response.send_message("Você não está nesta campanha.", ephemeral=True)
            return

        state.player_actions[interaction.user.id] = self.action_input.value
        await check_and_advance_turn(interaction, self.campaign_id)

# --- NOVOS MODAIS DE CRIAÇÃO DE PERSONAGEM ---
class SecondModalView(discord.ui.View):
    def __init__(self, dados_iniciais: dict[str, str]):
        super().__init__(timeout=300)
        self.dados_iniciais = dados_iniciais
    
    @discord.ui.button(label="Continuar Criação", style=discord.ButtonStyle.primary)
    async def continue_creation(self, interaction: discord.Interaction, button: discord.ui.Button['SecondModalView']):
        await interaction.response.send_modal(ProtagonistaDetalhesModal(dados_iniciais=self.dados_iniciais))

class ProtagonistaDetalhesModal(discord.ui.Modal, title="Criação de Protagonista (2/2)"):
    idade = discord.ui.TextInput(label='Idade', required=True)
    porte_fisico = discord.ui.TextInput(label='Porte Físico', style=discord.TextStyle.paragraph, required=True)
    aparencia = discord.ui.TextInput(label='Aparência Geral', style=discord.TextStyle.paragraph, required=True)
    itens = discord.ui.TextInput(label='Itens Iniciais', style=discord.TextStyle.paragraph, required=True)
    poderes = discord.ui.TextInput(label='Poderes e Habilidades', style=discord.TextStyle.paragraph, required=True)

    def __init__(self, dados_iniciais: dict[str, str]):
        super().__init__()
        self.dados_iniciais = dados_iniciais

    async def on_submit(self, interaction: discord.Interaction):
        # Combina os dados dos dois modais
        dados_completos = self.dados_iniciais.copy()
        dados_completos.update({
            'idade': self.idade.value,
            'porte_fisico': self.porte_fisico.value,
            'aparencia': self.aparencia.value,
            'itens': self.itens.value,
            'poderes': self.poderes.value,
        })

        try:
            protagonista_id = data_manager.create_protagonista(dados_completos)
            await interaction.response.send_message(
                f"Personagem **{dados_completos['nome']}** criado com sucesso!\\nGuarde este ID para usá-lo em campanhas: `{protagonista_id}`",
                ephemeral=True
            )
        except Exception as e:
            await interaction.response.send_message(f"Ocorreu um erro ao salvar o personagem: {e}", ephemeral=True)

class ProtagonistaModal(discord.ui.Modal, title='Criação de Protagonista (1/2)'):
    nome = discord.ui.TextInput(label='Nome', required=True)
    classe = discord.ui.TextInput(label='Classe', required=True)
    raca = discord.ui.TextInput(label='Raça', required=True)
    personalidade = discord.ui.TextInput(label='Personalidade', style=discord.TextStyle.paragraph, required=True)
    historico = discord.ui.TextInput(label='Histórico / Origem', style=discord.TextStyle.paragraph, required=True)

    async def on_submit(self, interaction: discord.Interaction):
        # Primeiro, responde à interação
        await interaction.response.defer(ephemeral=True)
        
        # Coleta os dados iniciais
        dados_iniciais = {
            'nome': self.nome.value,
            'classe': self.classe.value,
            'raca': self.raca.value,
            'personalidade': self.personalidade.value,
            'historico': self.historico.value,
        }
        
        # Envia uma mensagem temporária e depois o segundo modal
        await interaction.followup.send("Preenchendo dados adicionais...", ephemeral=True)
        
        # Cria uma nova interação para o segundo modal
        view = SecondModalView(dados_iniciais)
        await interaction.followup.send("Clique no botão abaixo para continuar:", view=view, ephemeral=True)

class CampanhaModal(discord.ui.Modal, title='Criação de Nova Campanha'):
    # (código do modal inalterado)
    tema = discord.ui.TextInput(label='Tema', required=True)
    tom = discord.ui.TextInput(label='Tom', required=True)
    cenario = discord.ui.TextInput(label='Cenário Inicial', style=discord.TextStyle.paragraph, required=True)
    def __init__(self, temp_campaign_id: str):
        super().__init__()
        self.temp_campaign_id = temp_campaign_id

    async def on_submit(self, interaction: discord.Interaction):
        await interaction.response.defer(thinking=True, ephemeral=True)
        state = active_campaigns.get(self.temp_campaign_id)
        if not state:
            await interaction.followup.send("A sessão desta campanha expirou.", ephemeral=True)
            return

        try:
            protagonistas_data = [data_manager.get_protagonista(p_id) for p_id in state.players.values()]
            protagonistas_validos = [p for p in protagonistas_data if p is not None]

            prompt = gemini_client.build_campaign_prompt(
                tema=self.tema.value,
                tom=self.tom.value,
                cenario=self.cenario.value,
                protagonistas=protagonistas_validos
            )
            response_text = gemini_client.generate_campaign_details(prompt)
            narrativa, acoes = parse_ia_response(response_text)

            campaign_data = {
                'nome_campanha': f"Campanha de {self.tema.value}",
                'tema': self.tema.value,
                'tom_narrativa': self.tom.value,
                'cenario_inicial': self.cenario.value,
                'data_criacao': interaction.created_at.strftime('%Y-%m-%d %H:%M:%S')
            }
            final_campaign_id = data_manager.create_campaign(campaign_data, list(state.players.values()))

            # Atualiza o estado da campanha
            state.campaign_id = final_campaign_id
            state.narrative = narrativa
            state.actions = acoes
            state.status = "running"
            state.player_actions = {player_id: None for player_id in state.players}
            state.turn_start_time = time.time()

            # Move o estado para o ID final e inicia o watcher
            active_campaigns[final_campaign_id] = state
            del active_campaigns[self.temp_campaign_id]
            if state.turn_start_time:
                asyncio.create_task(turn_timeout_watcher(final_campaign_id, state.turn_start_time))

            # Salva o estado inicial da campanha
            data_manager.save_campaign_state(state)

            await interaction.followup.send(
                f"--- **INÍCIO DA CAMPANHA: {campaign_data['nome_campanha']}** ---\n"
                f"**ID da Campanha:** `{final_campaign_id}` (use este ID para pausar, continuar ou carregar a campanha)",
                ephemeral=False
            )
            await send_long_message(
                interaction.followup,
                narrativa,
                view=ActionView(campaign_id=final_campaign_id, narrative=narrativa, actions=acoes)
            )
        except Exception as e:
            await interaction.followup.send(f"Ocorreu um erro ao iniciar a campanha: {e}", ephemeral=True)

# --- Cliente Discord e Comandos ---
class MyClient(discord.Client):
    # (código do cliente inalterado)
    def __init__(self, *, intents: discord.Intents):
        super().__init__(intents=intents)
        self.tree = app_commands.CommandTree(self)
        self.persistent_views_added = False
    async def on_ready(self):
        if not self.persistent_views_added:
            print("Carregando sessões de campanha anteriores...")
            session_files = data_manager.get_all_session_files()
            for file_path in session_files:
                state_data = data_manager.load_campaign_state(file_path)
                if state_data:
                    # Usa o método de classe para criar o objeto de estado
                    state = CampaignState.from_dict(state_data)
                    active_campaigns[state.campaign_id] = state
                    print(f"  - Campanha carregada: {state.campaign_id} (Status: {state.status})")
                    
                    # Recria a view apropriada para o estado da campanha
                    if state.status == "waiting":
                        self.add_view(JoinCampaignView(campaign_id=state.campaign_id))
                    elif state.status == "running":
                        self.add_view(ActionView(campaign_id=state.campaign_id, narrative=state.narrative, actions=state.actions))
                    # Campanhas pausadas não precisam ter a view recriada na inicialização.
                    # Elas serão reativadas com o comando /continuarcampanha.

            self.persistent_views_added = True
            print(f"{len(active_campaigns)} campanhas ativas carregadas.")

        print(f'Bot conectado como {self.user}')
        await self.tree.sync()
        print('Comandos sincronizados.')

client = MyClient(intents=discord.Intents.default())

# (código dos comandos inalterado)
@client.tree.command(name="ping")
async def ping(interaction: discord.Interaction): await interaction.response.send_message(f'Pong! {round(client.latency * 1000)}ms')
@client.tree.command(name="criarprotagonista", description="Cria um novo protagonista.")
async def criar_protagonista(interaction: discord.Interaction): await interaction.response.send_modal(ProtagonistaModal())
@client.tree.command(name="novacampanha", description="Prepara uma nova campanha de RPG para jogadores entrarem.")
@app_commands.describe(id_protagonista="O ID do seu protagonista para esta campanha.")
async def nova_campanha(interaction: discord.Interaction, id_protagonista: str):
    user_id = interaction.user.id
    guild_id = interaction.guild_id

    # Verifica se o protagonista existe
    if data_manager.get_protagonista(id_protagonista) is None:
        await interaction.response.send_message(f"Erro: Protagonista com ID `{id_protagonista}` não encontrado.", ephemeral=True)
        return

    # Cria um ID de campanha temporário
    temp_campaign_id = f"campaign_{guild_id}_{user_id}_{int(interaction.created_at.timestamp())}"

    # Cria o estado da campanha
    campaign_state = CampaignState(campaign_id=temp_campaign_id, creator_id=user_id)
    campaign_state.players[user_id] = id_protagonista
    campaign_state.channel_id = interaction.channel_id
    active_campaigns[temp_campaign_id] = campaign_state

    # Cria a View para entrar e iniciar
    view = JoinCampaignView(campaign_id=temp_campaign_id)

    # Envia a mensagem inicial
    await interaction.response.send_message(
        f"**Campanha iniciada por {interaction.user.mention}!**\n\n"
        f"Jogadores, cliquem em 'Entrar na Campanha' com o ID do seu protagonista. "
        f"Máximo de 4 jogadores.\n\n"
        f"**Jogadores Atuais:**\n- {interaction.user.mention}",
        view=view
    )
    message = await interaction.original_response()
    campaign_state.message_id = message.id


# --- Views para a Nova Lógica de Campanha ---
class JoinCampaignView(discord.ui.View):
    def __init__(self, campaign_id: str):
        super().__init__(timeout=None) # Persistente
        self.campaign_id = campaign_id

    async def update_player_list(self, interaction: discord.Interaction):
        """Atualiza a mensagem original com a lista de jogadores."""
        state = active_campaigns.get(self.campaign_id)
        if not state or not state.message_id:
            return

        player_mentions = [f"- <@{user_id}>" for user_id in state.players.keys()]
        player_list_str = "\n".join(player_mentions)

        if not isinstance(interaction.channel, (discord.TextChannel, discord.Thread)):
            return
        original_message = await interaction.channel.fetch_message(state.message_id)
        if original_message:
            await original_message.edit(
                content=(
                    f"**Campanha iniciada por <@{state.creator_id}>!**\n\n"
                    f"Jogadores, cliquem em 'Entrar na Campanha' com o ID do seu protagonista. "
                    f"Máximo de 4 jogadores.\n\n"
                    f"**Jogadores Atuais:**\n{player_list_str}"
                ),
                view=self
            )

    @discord.ui.button(label="Entrar na Campanha", style=discord.ButtonStyle.success, custom_id="join_campaign_button")
    async def join_campaign(self, interaction: discord.Interaction, button: discord.ui.Button['JoinCampaignView']):
        state = active_campaigns.get(self.campaign_id)
        if not state:
            await interaction.response.send_message("Esta campanha não está mais ativa.", ephemeral=True)
            return

        if interaction.user.id in state.players:
            await interaction.response.send_message("Você já entrou nesta campanha.", ephemeral=True)
            return

        if len(state.players) >= 4:
            await interaction.response.send_message("A campanha já atingiu o número máximo de 4 jogadores.", ephemeral=True)
            return

        await interaction.response.send_modal(JoinProtagonistaModal(campaign_id=self.campaign_id, view_to_update=self))

    @discord.ui.button(label="Iniciar Campanha", style=discord.ButtonStyle.primary, custom_id="start_campaign_button")
    async def start_campaign(self, interaction: discord.Interaction, button: discord.ui.Button['JoinCampaignView']):
        state = active_campaigns.get(self.campaign_id)
        if not state:
            await interaction.response.send_message("Esta campanha não está mais ativa.", ephemeral=True)
            return

        if interaction.user.id != state.creator_id:
            await interaction.response.send_message("Apenas o criador da campanha pode iniciá-la.", ephemeral=True)
            return

        if len(state.players) < 1: # No mínimo 1 jogador
            await interaction.response.send_message("É necessário pelo menos 1 jogador para iniciar a campanha.", ephemeral=True)
            return

        # Desativa os botões de entrar/iniciar
        if interaction.message:
            for item in self.children:
                if isinstance(item, discord.ui.Button):
                    item.disabled = True
            await interaction.message.edit(view=self)

        # Abre o modal para definir os detalhes da campanha, passando o ID temporário
        await interaction.response.send_modal(CampanhaModal(temp_campaign_id=self.campaign_id))


class JoinProtagonistaModal(discord.ui.Modal, title="Entrar na Campanha"):
    protagonista_id = discord.ui.TextInput(
        label="ID do seu Protagonista",
        placeholder="Cole aqui o ID do seu personagem criado com /criarprotagonista",
        required=True
    )

    def __init__(self, campaign_id: str, view_to_update: JoinCampaignView):
        super().__init__()
        self.campaign_id = campaign_id
        self.view_to_update = view_to_update

    async def on_submit(self, interaction: discord.Interaction):
        state = active_campaigns.get(self.campaign_id)
        if not state:
            await interaction.response.send_message("Esta campanha não está mais ativa.", ephemeral=True)
            return

        p_id = self.protagonista_id.value
        if data_manager.get_protagonista(p_id) is None:
            await interaction.response.send_message(f"Erro: Protagonista com ID `{p_id}` não encontrado.", ephemeral=True)
            return

        state.players[interaction.user.id] = p_id
        await interaction.response.defer()
        await self.view_to_update.update_player_list(interaction)


class PauseCampaignView(discord.ui.View):
    def __init__(self, campaign_id: str):
        super().__init__(timeout=60)
        self.campaign_id = campaign_id

    @discord.ui.button(label="Confirmar Pausa", style=discord.ButtonStyle.danger)
    async def confirm_pause(self, interaction: discord.Interaction, button: discord.ui.Button['PauseCampaignView']):
        state = active_campaigns.get(self.campaign_id)
        if not state or interaction.user.id not in state.players:
            await interaction.response.send_message("Você não pode pausar esta campanha.", ephemeral=True)
            return

        # Desativa a view e atualiza a mensagem
        for item in self.children:
            if isinstance(item, discord.ui.Button):
                item.disabled = True
        
        # Pausa a campanha
        state.status = "paused"
        state.player_actions = {player_id: None for player_id in state.players} # Limpa ações
        data_manager.save_campaign_state(state) # Salva o estado pausado
        data_manager.clear_campaign_summary(state.campaign_id) # Apaga o histórico de resumos

        await interaction.response.edit_message(content=f"**A campanha `{state.campaign_id}` foi pausada.**\n\nUse `/continuarcampanha` para retomar de onde pararam.", view=self)
        
        # Tenta remover a view da mensagem original da campanha
        if state.channel_id and state.message_id:
            try:
                channel = await client.fetch_channel(state.channel_id)
                if isinstance(channel, discord.TextChannel):
                    message = await channel.fetch_message(state.message_id)
                    await message.edit(view=None)
            except (discord.NotFound, discord.Forbidden):
                pass # A mensagem pode ter sido deletada

    @discord.ui.button(label="Cancelar", style=discord.ButtonStyle.secondary)
    async def cancel_pause(self, interaction: discord.Interaction, button: discord.ui.Button['PauseCampaignView']):
        for item in self.children:
            if isinstance(item, discord.ui.Button):
                item.disabled = True
        await interaction.response.edit_message(content="A pausa foi cancelada.", view=self)


@client.tree.command(name="pausarcampanha", description="Pausa uma campanha de RPG em andamento.")
@app_commands.describe(id_campanha="O ID da campanha que você deseja pausar.")
async def pausar_campanha(interaction: discord.Interaction, id_campanha: str):
    state = active_campaigns.get(id_campanha)

    if not state:
        await interaction.response.send_message(f"Campanha com ID `{id_campanha}` não encontrada ou não está ativa.", ephemeral=True)
        return

    if interaction.user.id not in state.players:
        await interaction.response.send_message("Você não é um jogador desta campanha e não pode pausá-la.", ephemeral=True)
        return

    if state.status != "running":
        await interaction.response.send_message(f"A campanha `{id_campanha}` não está em andamento, então não pode ser pausada.", ephemeral=True)
        return

    # Gera a mensagem de aviso
    warning_message = "**Aviso:** Pausar a campanha irá interromper o turno atual. Todas as ações já enviadas neste turno serão perdidas. Deseja continuar?"
    
    await interaction.response.send_message(
        warning_message,
        view=PauseCampaignView(campaign_id=id_campanha),
        ephemeral=True
    )


@client.tree.command(name="continuarcampanha", description="Retoma uma campanha de RPG que foi pausada.")
@app_commands.describe(id_campanha="O ID da campanha que você deseja continuar.")
async def continuar_campanha(interaction: discord.Interaction, id_campanha: str):
    state = active_campaigns.get(id_campanha)

    if not state:
        await interaction.response.send_message(f"Campanha com ID `{id_campanha}` não encontrada.", ephemeral=True)
        return

    if interaction.user.id not in state.players:
        await interaction.response.send_message("Você não é um jogador desta campanha e não pode continuá-la.", ephemeral=True)
        return

    if state.status != "paused":
        await interaction.response.send_message(f"A campanha `{id_campanha}` não está pausada.", ephemeral=True)
        return

    await interaction.response.defer()

    # Reativa a campanha
    state.status = "running"
    state.turn_start_time = time.time()
    data_manager.save_campaign_state(state)

    # Inicia o watcher do timeout
    if state.turn_start_time:
        asyncio.create_task(turn_timeout_watcher(state.campaign_id, state.turn_start_time))

    await interaction.followup.send(f"**A campanha `{state.campaign_id}` foi retomada!**")
    
    # Reenvia a última narrativa com as ações
    await send_long_message(
        interaction.followup,
        state.narrative,
        view=ActionView(campaign_id=state.campaign_id, narrative=state.narrative, actions=state.actions)
    )


def run_bot():
    if DISCORD_TOKEN: client.run(DISCORD_TOKEN)
    else: print("Erro: O token do Discord não foi configurado.")
