import csv
import os
import uuid
import json
from typing import Any
from .state_models import CampaignState

DATA_DIR = 'data'
CAMPAIGNS_FILE = os.path.join(DATA_DIR, 'campaigns.csv')
PROTAGONISTAS_DIR = os.path.join(DATA_DIR, 'protagonistas')
SESSIONS_DIR = os.path.join(DATA_DIR, 'sessions')
SUMMARIES_DIR = os.path.join(DATA_DIR, 'summaries')

CAMPAIGN_HEADER = [
    'id_campanha', 'nome_campanha', 'tema', 'tom_narrativa', 'cenario_inicial',
    'protagonista1_id', 'protagonista2_id', 'protagonista3_id', 'protagonista4_id', 'data_criacao'
]
PROTAGONISTA_HEADER = [
    'id_protagonista', 'nome', 'classe', 'raca', 'idade', 'porte_fisico',
    'aparencia', 'itens', 'poderes', 'personalidade', 'historico'
]

def initialize_data_storage():
    """Garante que os diretórios e arquivos de dados existam."""
    os.makedirs(PROTAGONISTAS_DIR, exist_ok=True)
    os.makedirs(SESSIONS_DIR, exist_ok=True)
    os.makedirs(SUMMARIES_DIR, exist_ok=True)
    if not os.path.exists(CAMPAIGNS_FILE):
        with open(CAMPAIGNS_FILE, 'w', newline='', encoding='utf-8') as f:
            writer = csv.writer(f)
            writer.writerow(CAMPAIGN_HEADER)

def create_protagonista(protagonista_data: dict[str, Any]):
    """Cria um novo protagonista e salva em um arquivo CSV dedicado."""
    protagonista_id = str(uuid.uuid4())
    protagonista_data['id_protagonista'] = protagonista_id
    
    file_path = os.path.join(PROTAGONISTAS_DIR, f"{protagonista_id}.csv")
    
    with open(file_path, 'w', newline='', encoding='utf-8') as f:
        writer = csv.DictWriter(f, fieldnames=PROTAGONISTA_HEADER)
        writer.writeheader()
        writer.writerow(protagonista_data)
        
    return protagonista_id

def create_campaign(campaign_data: dict[str, Any], protagonista_ids: list[str]):
    """Cria uma nova campanha e a adiciona ao campaigns.csv."""
    campaign_id = str(uuid.uuid4())
    campaign_data['id_campanha'] = campaign_id

    for i, p_id in enumerate(protagonista_ids, 1):
        campaign_data[f'protagonista{i}_id'] = p_id

    with open(CAMPAIGNS_FILE, 'a', newline='', encoding='utf-8') as f:
        writer = csv.DictWriter(f, fieldnames=CAMPAIGN_HEADER)
        writer.writerow(campaign_data)
        
    return campaign_id

# Inicializa o armazenamento de dados quando o módulo é importado
initialize_data_storage()
def get_campaigns():
    """Lê e retorna todas as campanhas do arquivo campaigns.csv."""
    if not os.path.exists(CAMPAIGNS_FILE):
        return []
    
    with open(CAMPAIGNS_FILE, 'r', newline='', encoding='utf-8') as f:
        reader = csv.DictReader(f)
        return list(reader)

def get_protagonista(protagonista_id: str):
    """Lê e retorna os dados de um protagonista específico."""
    file_path = os.path.join(PROTAGONISTAS_DIR, f"{protagonista_id}.csv")
    
    if not os.path.exists(file_path):
        return None
        
    with open(file_path, 'r', newline='', encoding='utf-8') as f:
        reader = csv.DictReader(f)
        return next(reader, None)

def save_campaign_state(state: CampaignState):
    """Salva o estado de uma campanha em um arquivo JSON."""
    file_path = os.path.join(SESSIONS_DIR, f"{state.campaign_id}.json")
    # Converte o objeto de estado para um dicionário serializável
    state_dict = {
        "campaign_id": state.campaign_id,
        "creator_id": state.creator_id,
        "players": state.players,
        "narrative": state.narrative,
        "actions": state.actions,
        "player_actions": state.player_actions,
        "status": state.status,
        "message_id": state.message_id,
        "channel_id": state.channel_id,
        "turn_start_time": state.turn_start_time,
        "turn_count": state.turn_count,
        "summary_interval": state.summary_interval,
    }
    with open(file_path, 'w', encoding='utf-8') as f:
        json.dump(state_dict, f, indent=4)

def load_campaign_state(file_path: str) -> dict[str, Any] | None:
    """Carrega o estado de uma campanha de um arquivo JSON."""
    if not os.path.exists(file_path):
        return None
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except (json.JSONDecodeError, IOError):
        return None

def get_all_session_files() -> list[str]:
    """Retorna uma lista com todos os caminhos de arquivos de sessão."""
    if not os.path.exists(SESSIONS_DIR):
        return []
    return [os.path.join(SESSIONS_DIR, f) for f in os.listdir(SESSIONS_DIR) if f.endswith('.json')]

def delete_campaign_state(campaign_id: str):
    """Deleta o arquivo de estado de uma campanha."""
    file_path = os.path.join(SESSIONS_DIR, f"{campaign_id}.json")
    if os.path.exists(file_path):
        os.remove(file_path)

def save_summary(campaign_id: str, summary_text: str):
    """Anexa um resumo ao arquivo de histórico da campanha."""
    file_path = os.path.join(SUMMARIES_DIR, f"{campaign_id}.txt")
    with open(file_path, 'a', encoding='utf-8') as f:
        f.write(f"--- RESUMO DO TURNO ---\\n{summary_text}\\n\\n")

def clear_campaign_summary(campaign_id: str):
    """Deleta o arquivo de resumo de uma campanha."""
    file_path = os.path.join(SUMMARIES_DIR, f"{campaign_id}.txt")
    if os.path.exists(file_path):
        os.remove(file_path)