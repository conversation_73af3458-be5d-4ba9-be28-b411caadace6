from typing import Any

class CampaignState:
    """Armazena o estado de uma campanha ativa."""
    def __init__(self, campaign_id: str, creator_id: int):
        self.campaign_id = campaign_id
        self.creator_id = creator_id
        self.players: dict[int, str] = {}  # Mapeia user_id para protagonista_id
        self.narrative: str = ""
        self.actions: list[str] = []
        self.player_actions: dict[int, str | None] = {}  # Mapeia user_id para a ação escolhida
        self.status: str = "waiting"  # waiting, running, paused
        self.message_id: int | None = None
        self.channel_id: int | None = None
        self.turn_start_time: float | None = None
        self.turn_count: int = 0
        self.summary_interval: int = 2

    @classmethod
    def from_dict(cls, data: dict[str, Any]):
        """Cria uma instância de CampaignState a partir de um dicionário."""
        state = cls(data['campaign_id'], data['creator_id'])
        state.players = {int(k): v for k, v in data.get('players', {}).items()}
        state.narrative = data.get('narrative', '')
        state.actions = data.get('actions', [])
        state.player_actions = {int(k): v for k, v in data.get('player_actions', {}).items()}
        state.status = data.get('status', 'waiting')
        state.message_id = data.get('message_id')
        state.channel_id = data.get('channel_id')
        state.turn_start_time = data.get('turn_start_time')
        state.turn_count = data.get('turn_count', 0)
        state.summary_interval = data.get('summary_interval', 2)
        return state