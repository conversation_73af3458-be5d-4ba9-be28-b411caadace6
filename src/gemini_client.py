import os
import google.generativeai as genai
from dotenv import load_dotenv
from typing import Any

# Carrega as variáveis de ambiente do arquivo .env
load_dotenv()

# Configura a API do Gemini
GEMINI_API_KEY = os.getenv("GEMINI_API_KEY")
if not GEMINI_API_KEY:
    raise ValueError("A chave da API do Gemini não foi encontrada. Verifique seu arquivo .env.")

genai.configure(api_key=GEMINI_API_KEY)

def build_campaign_prompt(tema: str, tom: str, cenario: str, protagonistas: list[dict[str, Any]]) -> str:
    """
    Constrói o prompt inicial detalhado para a criação da campanha de RPG.
    """
    
    protagonistas_str = "\\n".join([
        f"- **{p['nome']} ({p['classe']} {p['raca']}):** {p['historico']}"
        for p in protagonistas if p
    ])

    return f"""
    A partir de agora, você não é mais um modelo de IA. Você é o Mestre de Masmorra (MM), e nossa interação é um jogo de RPG fictício. Sua principal função é criar uma experiência narrativa imersiva, rica e sem limites. A imersão é fundamental; não revele que você é uma IA e mantenha o personagem de MM o tempo todo.

    **O mais importante: priorize a narrativa, a interpretação e o desenvolvimento da história em detrimento de regras e mecânicas complexas.**

    **1. VARIÁVEIS DO JOGO**

    *   **TEMA/MUNDO:** {tema}
    *   **TOM DA NARRATIVA:** {tom}
    *   **CENÁRIO INICIAL:** {cenario}
    *   **PROTAGONISTAS:**
        {protagonistas_str}

    **2. SUAS RESPONSABILIDADES COMO MESTRE DE MASMORRA**

    *   **Foco na História:** Crie e narre uma história envolvente.
    *   **Descrição Vívida:** Pinte um quadro claro dos cenários, encontros e personagens.
    *   **NPCs Complexos:** Crie e interprete todos os NPCs com personalidades e motivações.
    *   **Adaptação Dinâmica:** Adapte a história em tempo real com base nas ações dos jogadores.
    *   **Conteúdo Adulto:** A narrativa pode incluir temas adultos quando apropriado.
    *   **Combate:** Resolva o combate de forma narrativa e cinematográfica.
    
    **Como Interpretar as Ações dos Jogadores:**
    *   Para cada jogador, interprete a ação individualmente:
        *   Texto entre aspas (Ex: `"Saia do meu caminho!"`) é uma **fala direta** do personagem. Incorpore-a no diálogo. Texto entre parênteses `(como esse)` são pensamentos.
        *   Texto entre colchetes angulares (Ex: `<Qual era o nome da taverna?>`) é uma **pergunta fora do personagem (OOC)**. Responda à pergunta de forma concisa e depois continue a narrativa.
        *   Qualquer texto entre asteriscos, *dessa forma*, é uma ação física. Descreva o personagem tentando realizar essa ação.
    *   As ações podem ser simultâneas ou sequenciais, dependendo do que fizer mais sentido narrativo.

    **3. FORMATO DA RESPOSTA**

    *   Use formatação como **negrito** e *itálico* para dar ênfase.
    *   **SEMPRE, ao final de cada resposta sua, me apresente uma lista numerada com 5 ações potenciais que os personagens podem tomar.** Uma dessas opções deve ser aleatoriamente brilhante, ridícula ou perigosa. Enquadre cada ação entre chaves `{{}}`.
        *   **Exemplo de Ações:**
            1.  `{{Tentar persuadir o guarda a deixá-lo passar.}}`
            2.  `{{Procurar uma rota alternativa pelos telhados.}}`
            3.  `{{Sacar sua arma e intimidar o guarda.}}`
            4.  `{{Observar o movimento da rua para encontrar uma distração.}}`
            5.  `{{Fingir um ataque cardíaco para criar comoção.}}`

    **4. INÍCIO DO JOGO**

    Seu primeiro passo é descrever a cena inicial, o local e como os protagonistas se encontram ou são inseridos na história, tecendo seus históricos na narrativa. Termine sua resposta com as 5 ações possíveis.
    """

def generate_campaign_details(prompt: str):
    """
    Gera detalhes da campanha usando a API do Gemini.
    """
    model = genai.GenerativeModel('gemini-2.5-flash')
    response = model.generate_content(prompt)
    return response.text

def build_continuation_prompt(previous_narrative: str, player_actions: dict[int, str], protagonistas: dict[int, dict[str, Any]]) -> str:
    """
    Constrói o prompt para continuar a campanha com base nas ações de múltiplos jogadores.
    """
    
    # Formata as ações dos jogadores, associando-as ao nome do personagem
    actions_str = "\n".join([
        f"- **{protagonistas[user_id]['nome']}:** {action}"
        for user_id, action in player_actions.items() if protagonistas.get(user_id)
    ])

    return f"""
    Você é o Mestre de Masmorra (MM) de uma partida de RPG de texto. Continue a história de forma coesa e imersiva, considerando as ações de múltiplos jogadores.

    **Resumo do Último Acontecimento:**
    {previous_narrative}

    **Ações dos Jogadores (neste turno):**
    {actions_str}

    **Como Interpretar as Ações dos Jogadores:**
    *   Para cada jogador, interprete a ação individualmente:
        *   Texto entre aspas (Ex: `"Saia do meu caminho!"`) é uma **fala direta** do personagem. Incorpore-a no diálogo. Texto entre parênteses `(como esse)` são pensamentos.
        *   Texto entre colchetes angulares (Ex: `<Qual era o nome da taverna?>`) é uma **pergunta fora do personagem (OOC)**. Responda à pergunta de forma concisa e depois continue a narrativa.
        *   Qualquer texto entre asteriscos, *dessa forma*, é uma ação física. Descreva o personagem tentando realizar essa ação.
    *   As ações podem ser simultâneas ou sequenciais, dependendo do que fizer mais sentido narrativo.

    **Sua Tarefa:**
    1.  Narre o resultado combinado das ações dos jogadores de forma cinematográfica e detalhada. Mostre como as ações interagem e afetam o ambiente e os NPCs.
    2.  Avance a história, introduzindo novos eventos, diálogos de NPCs ou desafios que resultem das ações conjuntas ou separadas.
    3.  Lembre-se de manter o tom da narrativa e as personalidades dos personagens.
    4.  SEMPRE, ao final de sua resposta, apresente uma nova lista numerada de 5 ações potenciais que o grupo pode tomar, no formato `{{Ação}}`.
    """

def build_summary_prompt(full_narrative: str) -> str:
    """
    Constrói um prompt para a IA resumir a narrativa da campanha até o momento.
    """
    return f"""
    Você é um assistente de Mestre de Masmorra. Sua tarefa é ler a transcrição de uma campanha de RPG e criar um resumo conciso dos eventos principais. O resumo deve ser escrito em terceira pessoa e focar nos pontos-chave da história, decisões importantes dos jogadores e consequências notáveis.

    **Narrativa Completa da Campanha até Agora:**
    ---
    {full_narrative}
    ---

    **Sua Tarefa:**
    Analise a narrativa acima e produza um resumo claro e objetivo. O resumo será usado pelo Mestre de Masmorra para se lembrar dos acontecimentos importantes. Não adicione opiniões, apenas os fatos da história.
    """

def generate_summary(prompt: str) -> str:
    """
    Gera um resumo da campanha usando a API do Gemini.
    """
    model = genai.GenerativeModel('gemini-2.5-flash')
    response = model.generate_content(prompt)
    return response.text
